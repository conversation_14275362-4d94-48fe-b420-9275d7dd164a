"""
Base graph implementation following main Backspace agent patterns
"""

from abc import ABC, abstractmethod
# from langgraph.graph.state import CompiledGraph
from langgraph.pregel import RetryPolicy
import logging

logger = logging.getLogger(__name__)


class BaseGraph(ABC):
    """
    Abstract base class for all LangGraph implementations
    Based on main Backspace agent BaseGraph pattern
    """
    
    def __init__(self):
        self.retry_policy = RetryPolicy(
            retry_on=[Exception],  # Retry on any exception
            max_attempts=3,
            initial_interval=1.0,
            backoff_factor=2.0
        )
        logger.info("✅ BaseGraph initialized with retry policy")
    
    @abstractmethod
    def compile(self):
        """
        Compile the StateGraph - must be implemented by subclasses
        This follows the exact same pattern as main Backspace agent
        """
        raise NotImplementedError("Subclasses must implement compile()")