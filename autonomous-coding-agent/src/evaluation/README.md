# Enhanced Evaluation System

A comprehensive evaluation framework for autonomous coding agents with industry-standard metrics and practices.

## Features

### 🎯 Multi-Tier Evaluation Pipeline
- **Tier 1 (Quick)**: Fast validation tests (5-10 tasks, <5 min each)
- **Tier 2 (Standard)**: Comprehensive benchmark (50-100 tasks, 5-15 min each)
- **Tier 3 (Comprehensive)**: Production readiness (200+ tasks, 15-30 min each)

### 📊 Advanced Metrics
- **Pass@k Reliability**: Test consistency across multiple attempts
- **Trajectory Scoring**: Partial credit for correct intermediate steps
- **Context Efficiency**: Relevance of accessed files and code understanding
- **Code Correctness**: Syntax, runtime, and test validation
- **Requirement Adherence**: AI-powered task completion assessment
- **Code Quality**: Linting, formatting, and security analysis
- **Safety Evaluation**: Detection of dangerous patterns

### 🔍 Regression Detection
- **Version Comparison**: Automated regression detection between versions
- **Performance Trends**: Track improvements and degradations over time
- **Alert System**: Configurable thresholds for critical regressions
- **Detailed Reports**: Human-readable analysis of changes

### 🔗 LangSmith Integration
- **Auto-tracing**: Automatic tracing of Anthropic Claude calls
- **Observability**: Full visibility into agent decision-making process
- **Evaluation Datasets**: Integrated dataset management for consistent testing

## Usage

### Basic Evaluation
```bash
# Run quick validation
python -m evaluation.eval_runner eval quick

# Run standard benchmark
python -m evaluation.eval_runner eval standard

# Run comprehensive suite
python -m evaluation.eval_runner eval comprehensive
```

### Pass@k Reliability Testing
```bash
# Test reliability with 3 attempts per task
python -m evaluation.eval_runner eval standard --pass-at-k 3

# Test with 5 attempts for higher confidence
python -m evaluation.eval_runner eval quick --pass-at-k 5
```

### Performance Benchmarking
```bash
# Run performance benchmark
python -m evaluation.eval_runner benchmark --iterations 5

# Benchmark specific suite
python -m evaluation.eval_runner benchmark --suite comprehensive --iterations 3
```

### Regression Detection
```bash
# Compare versions
python -m evaluation.eval_runner compare --baseline v1.0 --current v1.1 --suite standard

# Generate regression report
python -m evaluation.eval_runner compare --baseline v1.0 --current v1.1 --suite standard --output regression_report.md
```

### List Available Suites
```bash
python -m evaluation.eval_runner list
```

## Configuration

### Environment Variables
```bash
# LangSmith Integration
export LANGSMITH_API_KEY="your-api-key"
export LANGSMITH_PROJECT="autonomous-coding-agent"

# Anthropic API
export ANTHROPIC_API_KEY="your-api-key"

# GitHub Token
export GITHUB_TOKEN="your-token"
```

### Settings
Configure evaluation parameters in your settings file:
```python
# Regression thresholds
REGRESSION_THRESHOLDS = {
    "critical": 0.3,    # 30% drop
    "high": 0.2,        # 20% drop  
    "medium": 0.1,      # 10% drop
    "low": 0.05         # 5% drop
}

# Evaluation timeouts
EVAL_TIMEOUTS = {
    "quick": 5,         # 5 minutes
    "standard": 15,     # 15 minutes
    "comprehensive": 30 # 30 minutes
}
```

## Evaluation Datasets

### Quick Validation Suite (Tier 1)
Fast validation tests for basic functionality:
- Simple API endpoints
- Basic bug fixes
- Configuration changes
- Logging statements
- Simple validations

### Standard Benchmark Suite (Tier 2)
Real-world coding scenarios:
- CRUD API implementation
- Authentication systems
- Caching mechanisms
- Database migrations
- Error handling
- Async processing
- File handling
- API versioning

### Comprehensive Suite (Tier 3)
Production-ready complex scenarios:
- Microservice architectures
- Performance optimization
- Security hardening
- Distributed systems
- Data pipelines
- ML model deployment
- Complex refactoring
- Real-time systems
- Integration testing

## Metrics Explained

### Pass@k Reliability Score
Measures how consistently the agent performs across multiple attempts:
- **k=1**: Standard evaluation (single attempt)
- **k=3**: Reliability testing (3 attempts)
- **k=5**: High confidence testing (5 attempts)

Formula: `success_rate * (1 - min(variance, 0.5))`

### Trajectory Score
Evaluates the quality of intermediate steps:
- **Step Quality**: Appropriateness of each action
- **Goal Progress**: Movement toward task completion
- **Efficiency**: Resource utilization
- **Error Handling**: Recovery from mistakes

Formula: `(0.4 * avg_step_score) + (0.6 * final_completion_score)`

### Context Efficiency
Measures how well the agent identifies relevant code:
- **Precision**: Relevant files accessed / Total files accessed
- **Recall**: Expected files accessed / Total expected files
- **F1 Score**: Harmonic mean of precision and recall

Formula: `(0.6 * precision) + (0.4 * recall)`

## Integration Examples

### With LangSmith
```python
from evaluation.eval_framework import AutonomousCodingAgentEvaluator
from utils.langsmith_config import setup_langsmith, get_traced_anthropic_client
import anthropic

# Setup LangSmith
setup_langsmith()

# Create traced client
client = anthropic.Anthropic(api_key="your-key")
traced_client = get_traced_anthropic_client(client)

# Initialize evaluator
evaluator = AutonomousCodingAgentEvaluator(
    anthropic_api_key="your-key",
    github_token="your-token"
)
```

### Custom Evaluation Task
```python
from evaluation.eval_framework import EvalTask

custom_task = EvalTask(
    task_id="custom_feature",
    repo_url="https://github.com/your-org/your-repo",
    prompt="Implement a new feature with specific requirements",
    expected_changes=["feature.py", "tests/test_feature.py"],
    success_criteria={"feature_implemented": True, "tests_pass": True},
    difficulty="medium",
    category="feature",
    timeout_minutes=20
)
```

## Troubleshooting

### Common Issues

1. **LangSmith Tracing Not Working**
   - Ensure `LANGSMITH_API_KEY` is set
   - Check if `langsmith` package is installed
   - Verify project name in `LANGSMITH_PROJECT`

2. **Anthropic API Errors**
   - Verify `ANTHROPIC_API_KEY` is valid
   - Check rate limits
   - Ensure sufficient credits

3. **GitHub Token Issues**
   - Verify token has required permissions
   - Check token expiration
   - Ensure access to target repositories

4. **Evaluation Timeouts**
   - Increase timeout values for complex tasks
   - Check network connectivity
   - Verify sandbox environment

### Debug Mode
```bash
# Run with debug logging
PYTHONPATH=. python -m evaluation.eval_runner eval quick --debug
```

## Contributing

When adding new evaluation tasks:

1. **Define Clear Success Criteria**: Specify measurable outcomes
2. **Set Appropriate Timeouts**: Balance thoroughness with efficiency
3. **Include Expected Changes**: List files that should be modified
4. **Test Across Difficulties**: Ensure balanced difficulty distribution
5. **Add Documentation**: Document task purpose and expected behavior

## Architecture

```
evaluation/
├── eval_framework.py         # Core evaluation framework
├── enhanced_evaluators.py    # Pass@k, trajectory, context evaluators
├── tiered_datasets.py        # Evaluation datasets by tier
├── regression_detection.py   # Version comparison and alerts
├── eval_runner.py            # CLI interface
└── README.md                 # This documentation
```

## Best Practices

1. **Regular Benchmarking**: Run evaluations on every major change
2. **Tiered Testing**: Start with quick validation, then comprehensive
3. **Regression Monitoring**: Compare versions to catch degradations
4. **Pass@k Testing**: Use k>1 for critical components
5. **LangSmith Integration**: Enable tracing for observability
6. **Result Storage**: Archive results for historical analysis
7. **Alert Configuration**: Set appropriate regression thresholds