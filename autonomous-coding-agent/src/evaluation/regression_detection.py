"""
Regression detection system for comparing evaluation results across versions
"""

import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path
import statistics

from .eval_framework import EvalResult, EvalSuite, PassAtKResult

logger = logging.getLogger(__name__)


@dataclass
class VersionComparison:
    """Comparison between two versions"""
    baseline_version: str
    current_version: str
    suite_name: str
    overall_regression: bool
    score_delta: float
    success_rate_delta: float
    task_comparisons: List[Dict[str, Any]]
    significant_regressions: List[str]
    significant_improvements: List[str]
    timestamp: str


@dataclass
class RegressionAlert:
    """Alert for significant regression"""
    task_id: str
    metric: str
    baseline_score: float
    current_score: float
    delta: float
    severity: str  # 'low', 'medium', 'high', 'critical'


class RegressionDetector:
    """Detects regressions by comparing evaluation results across versions"""
    
    def __init__(self, results_dir: str = "evaluation_results"):
        self.results_dir = Path(results_dir)
        self.results_dir.mkdir(exist_ok=True)
        
        # Regression thresholds
        self.thresholds = {
            "critical": 0.3,    # 30% drop
            "high": 0.2,        # 20% drop
            "medium": 0.1,      # 10% drop
            "low": 0.05         # 5% drop
        }
    
    def save_results(self, results: Dict[str, Any], version: str, suite_name: str) -> Path:
        """Save evaluation results for version comparison"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{suite_name}_{version}_{timestamp}.json"
        filepath = self.results_dir / filename
        
        # Add metadata
        results_with_metadata = {
            "version": version,
            "suite_name": suite_name,
            "timestamp": timestamp,
            "results": results
        }
        
        with open(filepath, 'w') as f:
            json.dump(results_with_metadata, f, indent=2)
        
        logger.info(f"Saved results for {suite_name} v{version} to {filepath}")
        return filepath
    
    def compare_versions(self, baseline_version: str, current_version: str, 
                        suite_name: str) -> VersionComparison:
        """Compare two versions and detect regressions"""
        
        # Load baseline results
        baseline_results = self._load_latest_results(baseline_version, suite_name)
        if not baseline_results:
            raise ValueError(f"No baseline results found for {suite_name} v{baseline_version}")
        
        # Load current results
        current_results = self._load_latest_results(current_version, suite_name)
        if not current_results:
            raise ValueError(f"No current results found for {suite_name} v{current_version}")
        
        # Compare overall metrics
        baseline_success_rate = baseline_results["results"]["success_rate"]
        current_success_rate = current_results["results"]["success_rate"]
        success_rate_delta = current_success_rate - baseline_success_rate
        
        # Compare average scores
        baseline_avg_score = self._calculate_average_score(baseline_results["results"]["average_scores"])
        current_avg_score = self._calculate_average_score(current_results["results"]["average_scores"])
        score_delta = current_avg_score - baseline_avg_score
        
        # Compare individual tasks
        task_comparisons = self._compare_individual_tasks(
            baseline_results["results"]["individual_results"],
            current_results["results"]["individual_results"]
        )
        
        # Identify significant changes
        significant_regressions = []
        significant_improvements = []
        
        for task_comp in task_comparisons:
            if task_comp["overall_delta"] < -self.thresholds["medium"]:
                significant_regressions.append(task_comp["task_id"])
            elif task_comp["overall_delta"] > self.thresholds["medium"]:
                significant_improvements.append(task_comp["task_id"])
        
        # Determine if there's overall regression
        overall_regression = (
            success_rate_delta < -self.thresholds["low"] or
            score_delta < -self.thresholds["low"] or
            len(significant_regressions) > len(significant_improvements)
        )
        
        return VersionComparison(
            baseline_version=baseline_version,
            current_version=current_version,
            suite_name=suite_name,
            overall_regression=overall_regression,
            score_delta=score_delta,
            success_rate_delta=success_rate_delta,
            task_comparisons=task_comparisons,
            significant_regressions=significant_regressions,
            significant_improvements=significant_improvements,
            timestamp=datetime.now().isoformat()
        )
    
    def generate_alerts(self, comparison: VersionComparison) -> List[RegressionAlert]:
        """Generate alerts for significant regressions"""
        alerts = []
        
        # Check overall metrics
        if comparison.success_rate_delta < -self.thresholds["medium"]:
            severity = self._calculate_severity(abs(comparison.success_rate_delta))
            alerts.append(RegressionAlert(
                task_id="OVERALL",
                metric="success_rate",
                baseline_score=0.0,  # Will be filled by caller
                current_score=0.0,   # Will be filled by caller
                delta=comparison.success_rate_delta,
                severity=severity
            ))
        
        # Check individual tasks
        for task_comp in comparison.task_comparisons:
            if task_comp["overall_delta"] < -self.thresholds["low"]:
                severity = self._calculate_severity(abs(task_comp["overall_delta"]))
                alerts.append(RegressionAlert(
                    task_id=task_comp["task_id"],
                    metric="overall_score",
                    baseline_score=task_comp["baseline_overall"],
                    current_score=task_comp["current_overall"],
                    delta=task_comp["overall_delta"],
                    severity=severity
                ))
        
        return alerts
    
    def generate_report(self, comparison: VersionComparison) -> str:
        """Generate human-readable regression report"""
        report = []
        report.append(f"# Regression Report: {comparison.suite_name}")
        report.append(f"**Baseline:** {comparison.baseline_version}")
        report.append(f"**Current:** {comparison.current_version}")
        report.append(f"**Generated:** {comparison.timestamp}")
        report.append("")
        
        # Overall status
        status = "🔴 REGRESSION DETECTED" if comparison.overall_regression else "🟢 NO REGRESSION"
        report.append(f"## Overall Status: {status}")
        report.append("")
        
        # Key metrics
        report.append("## Key Metrics")
        report.append(f"- **Success Rate Change:** {comparison.success_rate_delta:+.1%}")
        report.append(f"- **Average Score Change:** {comparison.score_delta:+.3f}")
        report.append(f"- **Tasks with Regression:** {len(comparison.significant_regressions)}")
        report.append(f"- **Tasks with Improvement:** {len(comparison.significant_improvements)}")
        report.append("")
        
        # Significant regressions
        if comparison.significant_regressions:
            report.append("## 🔴 Significant Regressions")
            for task_id in comparison.significant_regressions:
                task_comp = next(t for t in comparison.task_comparisons if t["task_id"] == task_id)
                report.append(f"- **{task_id}:** {task_comp['overall_delta']:+.3f} "
                             f"({task_comp['baseline_overall']:.3f} → {task_comp['current_overall']:.3f})")
            report.append("")
        
        # Significant improvements
        if comparison.significant_improvements:
            report.append("## 🟢 Significant Improvements")
            for task_id in comparison.significant_improvements:
                task_comp = next(t for t in comparison.task_comparisons if t["task_id"] == task_id)
                report.append(f"- **{task_id}:** {task_comp['overall_delta']:+.3f} "
                             f"({task_comp['baseline_overall']:.3f} → {task_comp['current_overall']:.3f})")
            report.append("")
        
        # Detailed breakdown
        report.append("## Detailed Task Breakdown")
        for task_comp in comparison.task_comparisons:
            status_emoji = "🔴" if task_comp["overall_delta"] < -self.thresholds["low"] else \
                          "🟢" if task_comp["overall_delta"] > self.thresholds["low"] else "🟡"
            
            report.append(f"### {status_emoji} {task_comp['task_id']}")
            report.append(f"- **Overall:** {task_comp['baseline_overall']:.3f} → {task_comp['current_overall']:.3f} ({task_comp['overall_delta']:+.3f})")
            report.append(f"- **Correctness:** {task_comp['correctness_delta']:+.3f}")
            report.append(f"- **Adherence:** {task_comp['adherence_delta']:+.3f}")
            report.append(f"- **Quality:** {task_comp['quality_delta']:+.3f}")
            report.append(f"- **Safety:** {task_comp['safety_delta']:+.3f}")
            report.append("")
        
        return "\n".join(report)
    
    def _load_latest_results(self, version: str, suite_name: str) -> Optional[Dict[str, Any]]:
        """Load the latest results for a version and suite"""
        pattern = f"{suite_name}_{version}_*.json"
        files = list(self.results_dir.glob(pattern))
        
        if not files:
            return None
        
        # Get the most recent file
        latest_file = max(files, key=lambda f: f.stat().st_mtime)
        
        with open(latest_file, 'r') as f:
            return json.load(f)
    
    def _calculate_average_score(self, scores: Dict[str, float]) -> float:
        """Calculate average score from score dictionary"""
        return sum(scores.values()) / len(scores)
    
    def _compare_individual_tasks(self, baseline_tasks: List[Dict[str, Any]], 
                                 current_tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Compare individual task results"""
        comparisons = []
        
        # Create lookup for current tasks
        current_lookup = {task["task_id"]: task for task in current_tasks}
        
        for baseline_task in baseline_tasks:
            task_id = baseline_task["task_id"]
            
            if task_id not in current_lookup:
                logger.warning(f"Task {task_id} not found in current results")
                continue
            
            current_task = current_lookup[task_id]
            
            # Calculate overall scores
            baseline_overall = (
                baseline_task["code_correctness"] +
                baseline_task["requirement_adherence"] +
                baseline_task["code_quality"] +
                baseline_task["safety_score"]
            ) / 4
            
            current_overall = (
                current_task["code_correctness"] +
                current_task["requirement_adherence"] +
                current_task["code_quality"] +
                current_task["safety_score"]
            ) / 4
            
            comparison = {
                "task_id": task_id,
                "baseline_overall": baseline_overall,
                "current_overall": current_overall,
                "overall_delta": current_overall - baseline_overall,
                "correctness_delta": current_task["code_correctness"] - baseline_task["code_correctness"],
                "adherence_delta": current_task["requirement_adherence"] - baseline_task["requirement_adherence"],
                "quality_delta": current_task["code_quality"] - baseline_task["code_quality"],
                "safety_delta": current_task["safety_score"] - baseline_task["safety_score"]
            }
            
            comparisons.append(comparison)
        
        return comparisons
    
    def _calculate_severity(self, delta: float) -> str:
        """Calculate severity level based on delta"""
        if delta >= self.thresholds["critical"]:
            return "critical"
        elif delta >= self.thresholds["high"]:
            return "high"
        elif delta >= self.thresholds["medium"]:
            return "medium"
        else:
            return "low"


class ContinuousBenchmarkManager:
    """Manages continuous benchmarking and regression detection"""
    
    def __init__(self, results_dir: str = "evaluation_results"):
        self.regression_detector = RegressionDetector(results_dir)
        self.benchmark_history = []
    
    async def run_benchmark_cycle(self, evaluator, suite: EvalSuite, version: str) -> Dict[str, Any]:
        """Run a complete benchmark cycle with regression detection"""
        logger.info(f"Starting benchmark cycle for {suite.name} v{version}")
        
        # Run evaluation
        results = await evaluator.evaluate_suite(suite)
        
        # Save results
        self.regression_detector.save_results(results, version, suite.name)
        
        # Check for regressions if we have baseline
        regression_info = None
        if self.benchmark_history:
            last_version = self.benchmark_history[-1]["version"]
            try:
                comparison = self.regression_detector.compare_versions(
                    last_version, version, suite.name
                )
                alerts = self.regression_detector.generate_alerts(comparison)
                
                regression_info = {
                    "comparison": asdict(comparison),
                    "alerts": [asdict(alert) for alert in alerts],
                    "report": self.regression_detector.generate_report(comparison)
                }
                
                if comparison.overall_regression:
                    logger.warning(f"Regression detected in {suite.name} v{version}")
                else:
                    logger.info(f"No regression detected in {suite.name} v{version}")
                    
            except Exception as e:
                logger.error(f"Failed to check for regressions: {e}")
        
        # Update history
        self.benchmark_history.append({
            "version": version,
            "suite_name": suite.name,
            "timestamp": datetime.now().isoformat(),
            "results": results,
            "regression_info": regression_info
        })
        
        return {
            "evaluation_results": results,
            "regression_info": regression_info,
            "benchmark_cycle_complete": True
        }
    
    def get_benchmark_history(self, suite_name: str = None) -> List[Dict[str, Any]]:
        """Get benchmark history, optionally filtered by suite"""
        if suite_name:
            return [h for h in self.benchmark_history if h["suite_name"] == suite_name]
        return self.benchmark_history
    
    def get_performance_trends(self, suite_name: str, metric: str = "success_rate") -> Dict[str, Any]:
        """Get performance trends over time"""
        history = self.get_benchmark_history(suite_name)
        
        if not history:
            return {"error": "No history available"}
        
        timestamps = []
        values = []
        
        for entry in history:
            timestamps.append(entry["timestamp"])
            
            if metric == "success_rate":
                values.append(entry["results"]["success_rate"])
            elif metric == "average_score":
                avg_scores = entry["results"]["average_scores"]
                values.append(sum(avg_scores.values()) / len(avg_scores))
            else:
                values.append(entry["results"]["average_scores"].get(metric, 0))
        
        # Calculate trend
        if len(values) >= 2:
            trend = "improving" if values[-1] > values[0] else "declining"
            trend_slope = (values[-1] - values[0]) / len(values)
        else:
            trend = "stable"
            trend_slope = 0
        
        return {
            "metric": metric,
            "timestamps": timestamps,
            "values": values,
            "trend": trend,
            "trend_slope": trend_slope,
            "current_value": values[-1] if values else 0,
            "best_value": max(values) if values else 0,
            "worst_value": min(values) if values else 0
        }