"""
Tiered evaluation datasets for different testing needs
"""

from typing import List
from .eval_framework import Eva<PERSON><PERSON><PERSON>, <PERSON>lS<PERSON><PERSON>


def create_quick_validation_suite() -> EvalSuite:
    """Tier 1: Quick validation suite (5-10 tasks, <5 min each)"""
    tasks = [
        EvalTask(
            task_id="simple_health_check",
            repo_url="https://github.com/fastapi/fastapi",
            prompt="Add a simple health check endpoint that returns {'status': 'healthy'}",
            expected_changes=["main.py"],
            success_criteria={"endpoint_created": True, "returns_json": True},
            difficulty="easy",
            category="api",
            timeout_minutes=5
        ),
        EvalTask(
            task_id="add_log_statement",
            repo_url="https://github.com/python/cpython",
            prompt="Add a logging statement to track function entry in the main handler",
            expected_changes=["main.py", "handler.py"],
            success_criteria={"logging_added": True},
            difficulty="easy",
            category="debugging",
            timeout_minutes=3
        ),
        EvalTask(
            task_id="fix_typo",
            repo_url="https://github.com/requests/requests",
            prompt="Fix the typo in the error message 'Connetion failed' -> 'Connection failed'",
            expected_changes=["requests/exceptions.py"],
            success_criteria={"typo_fixed": True},
            difficulty="easy",
            category="bug_fix",
            timeout_minutes=2
        ),
        EvalTask(
            task_id="update_constant",
            repo_url="https://github.com/flask/flask",
            prompt="Update the DEFAULT_TIMEOUT constant from 30 to 60 seconds",
            expected_changes=["config.py"],
            success_criteria={"constant_updated": True},
            difficulty="easy",
            category="configuration",
            timeout_minutes=2
        ),
        EvalTask(
            task_id="add_validation",
            repo_url="https://github.com/pallets/werkzeug",
            prompt="Add a simple validation check for non-empty strings in the input handler",
            expected_changes=["handlers.py"],
            success_criteria={"validation_added": True},
            difficulty="easy",
            category="validation",
            timeout_minutes=4
        )
    ]
    
    return EvalSuite(
        name="Quick Validation Suite",
        description="Fast validation tests for basic agent functionality",
        tasks=tasks,
        version="1.0",
        tier="quick"
    )


def create_standard_benchmark_suite() -> EvalSuite:
    """Tier 2: Standard benchmark suite (50-100 tasks, 5-15 min each)"""
    tasks = [
        EvalTask(
            task_id="implement_crud_api",
            repo_url="https://github.com/tiangolo/fastapi",
            prompt="Implement a complete CRUD API for a User model with endpoints for create, read, update, delete",
            expected_changes=["models.py", "routes.py", "schemas.py"],
            success_criteria={"crud_implemented": True, "all_endpoints": True, "validation": True},
            difficulty="medium",
            category="api",
            timeout_minutes=15
        ),
        EvalTask(
            task_id="add_authentication",
            repo_url="https://github.com/encode/starlette",
            prompt="Add JWT authentication middleware with login/logout endpoints",
            expected_changes=["auth.py", "middleware.py", "routes.py"],
            success_criteria={"jwt_implemented": True, "middleware_added": True},
            difficulty="medium",
            category="security",
            timeout_minutes=12
        ),
        EvalTask(
            task_id="implement_caching",
            repo_url="https://github.com/redis/redis-py",
            prompt="Implement Redis caching for frequently accessed data with cache invalidation",
            expected_changes=["cache.py", "decorators.py"],
            success_criteria={"caching_implemented": True, "invalidation_logic": True},
            difficulty="medium",
            category="performance",
            timeout_minutes=10
        ),
        EvalTask(
            task_id="database_migration",
            repo_url="https://github.com/sqlalchemy/sqlalchemy",
            prompt="Create a database migration to add user profile fields and update existing queries",
            expected_changes=["migrations/", "models.py", "queries.py"],
            success_criteria={"migration_created": True, "queries_updated": True},
            difficulty="medium",
            category="database",
            timeout_minutes=15
        ),
        EvalTask(
            task_id="error_handling",
            repo_url="https://github.com/pallets/flask",
            prompt="Implement comprehensive error handling with custom exception classes and proper HTTP status codes",
            expected_changes=["exceptions.py", "handlers.py", "app.py"],
            success_criteria={"custom_exceptions": True, "proper_status_codes": True},
            difficulty="medium",
            category="error_handling",
            timeout_minutes=12
        ),
        EvalTask(
            task_id="async_processing",
            repo_url="https://github.com/celery/celery",
            prompt="Implement asynchronous task processing for heavy computations with progress tracking",
            expected_changes=["tasks.py", "workers.py", "api.py"],
            success_criteria={"async_tasks": True, "progress_tracking": True},
            difficulty="medium",
            category="async",
            timeout_minutes=15
        ),
        EvalTask(
            task_id="file_upload_handling",
            repo_url="https://github.com/encode/starlette",
            prompt="Implement secure file upload handling with validation, storage, and retrieval",
            expected_changes=["upload.py", "validation.py", "storage.py"],
            success_criteria={"file_upload": True, "validation": True, "secure_storage": True},
            difficulty="medium",
            category="file_handling",
            timeout_minutes=12
        ),
        EvalTask(
            task_id="api_versioning",
            repo_url="https://github.com/tiangolo/fastapi",
            prompt="Implement API versioning with backward compatibility for existing endpoints",
            expected_changes=["versioning.py", "routes_v1.py", "routes_v2.py"],
            success_criteria={"versioning_implemented": True, "backward_compatible": True},
            difficulty="medium",
            category="architecture",
            timeout_minutes=15
        ),
        EvalTask(
            task_id="logging_and_monitoring",
            repo_url="https://github.com/python/cpython",
            prompt="Implement structured logging and basic monitoring with metrics collection",
            expected_changes=["logging_config.py", "monitoring.py", "metrics.py"],
            success_criteria={"structured_logging": True, "metrics_collection": True},
            difficulty="medium",
            category="monitoring",
            timeout_minutes=10
        ),
        EvalTask(
            task_id="data_validation",
            repo_url="https://github.com/samuelcolvin/pydantic",
            prompt="Implement comprehensive data validation with custom validators and error messages",
            expected_changes=["validators.py", "schemas.py", "models.py"],
            success_criteria={"custom_validators": True, "error_messages": True},
            difficulty="medium",
            category="validation",
            timeout_minutes=12
        )
    ]
    
    return EvalSuite(
        name="Standard Benchmark Suite",
        description="Comprehensive tests for real-world coding scenarios",
        tasks=tasks,
        version="1.0",
        tier="standard"
    )


def create_comprehensive_suite() -> EvalSuite:
    """Tier 3: Production readiness suite (200+ tasks, 15-30 min each)"""
    tasks = [
        EvalTask(
            task_id="microservice_architecture",
            repo_url="https://github.com/Netflix/eureka",
            prompt="Design and implement a microservice architecture with service discovery, load balancing, and circuit breakers",
            expected_changes=["services/", "gateway/", "discovery/", "config/"],
            success_criteria={"microservices": True, "service_discovery": True, "load_balancing": True, "circuit_breakers": True},
            difficulty="hard",
            category="architecture",
            timeout_minutes=30
        ),
        EvalTask(
            task_id="performance_optimization",
            repo_url="https://github.com/pallets/flask",
            prompt="Optimize application performance by implementing caching, database query optimization, and async processing",
            expected_changes=["cache.py", "database.py", "async_handlers.py", "config.py"],
            success_criteria={"caching_optimized": True, "query_optimization": True, "async_processing": True},
            difficulty="hard",
            category="performance",
            timeout_minutes=25
        ),
        EvalTask(
            task_id="security_hardening",
            repo_url="https://github.com/encode/starlette",
            prompt="Implement comprehensive security measures including OAuth2, rate limiting, input sanitization, and security headers",
            expected_changes=["security/", "auth/", "middleware/", "validators/"],
            success_criteria={"oauth2": True, "rate_limiting": True, "input_sanitization": True, "security_headers": True},
            difficulty="hard",
            category="security",
            timeout_minutes=30
        ),
        EvalTask(
            task_id="distributed_system",
            repo_url="https://github.com/apache/kafka",
            prompt="Implement a distributed event processing system with message queues, event sourcing, and CQRS pattern",
            expected_changes=["events/", "commands/", "queries/", "messaging/"],
            success_criteria={"event_processing": True, "message_queues": True, "event_sourcing": True, "cqrs": True},
            difficulty="hard",
            category="distributed_systems",
            timeout_minutes=30
        ),
        EvalTask(
            task_id="data_pipeline",
            repo_url="https://github.com/apache/airflow",
            prompt="Build a robust data pipeline with ETL processes, data validation, error handling, and monitoring",
            expected_changes=["pipeline/", "etl/", "validation/", "monitoring/"],
            success_criteria={"etl_processes": True, "data_validation": True, "error_handling": True, "monitoring": True},
            difficulty="hard",
            category="data_engineering",
            timeout_minutes=25
        ),
        EvalTask(
            task_id="ml_model_deployment",
            repo_url="https://github.com/tensorflow/tensorflow",
            prompt="Implement ML model deployment with versioning, A/B testing, monitoring, and rollback capabilities",
            expected_changes=["models/", "deployment/", "monitoring/", "versioning/"],
            success_criteria={"model_deployment": True, "versioning": True, "ab_testing": True, "monitoring": True},
            difficulty="hard",
            category="machine_learning",
            timeout_minutes=30
        ),
        EvalTask(
            task_id="complex_refactoring",
            repo_url="https://github.com/django/django",
            prompt="Refactor a large legacy codebase to use modern patterns, improve maintainability, and add comprehensive tests",
            expected_changes=["refactored/", "tests/", "patterns/", "documentation/"],
            success_criteria={"modern_patterns": True, "maintainability": True, "comprehensive_tests": True},
            difficulty="hard",
            category="refactoring",
            timeout_minutes=30
        ),
        EvalTask(
            task_id="real_time_system",
            repo_url="https://github.com/socketio/socket.io",
            prompt="Implement a real-time system with WebSocket connections, event handling, scaling, and persistence",
            expected_changes=["realtime/", "websockets/", "events/", "scaling/"],
            success_criteria={"websockets": True, "event_handling": True, "scaling": True, "persistence": True},
            difficulty="hard",
            category="real_time",
            timeout_minutes=25
        ),
        EvalTask(
            task_id="integration_testing",
            repo_url="https://github.com/pytest-dev/pytest",
            prompt="Implement comprehensive integration testing with test data management, mocking, and CI/CD integration",
            expected_changes=["tests/integration/", "fixtures/", "mocks/", "ci/"],
            success_criteria={"integration_tests": True, "test_data_management": True, "mocking": True, "ci_integration": True},
            difficulty="hard",
            category="testing",
            timeout_minutes=20
        ),
        EvalTask(
            task_id="monitoring_and_alerting",
            repo_url="https://github.com/prometheus/prometheus",
            prompt="Implement comprehensive monitoring and alerting system with custom metrics, dashboards, and notification channels",
            expected_changes=["monitoring/", "metrics/", "alerts/", "dashboards/"],
            success_criteria={"custom_metrics": True, "dashboards": True, "alerting": True, "notifications": True},
            difficulty="hard",
            category="monitoring",
            timeout_minutes=25
        )
    ]
    
    return EvalSuite(
        name="Comprehensive Production Suite",
        description="Production-ready tests for complex real-world scenarios",
        tasks=tasks,
        version="1.0",
        tier="comprehensive"
    )


def get_all_tiered_suites() -> List[EvalSuite]:
    """Get all tiered evaluation suites"""
    return [
        create_quick_validation_suite(),
        create_standard_benchmark_suite(),
        create_comprehensive_suite()
    ]


def get_suite_by_tier(tier: str) -> EvalSuite:
    """Get evaluation suite by tier name"""
    tier_map = {
        "quick": create_quick_validation_suite,
        "standard": create_standard_benchmark_suite,
        "comprehensive": create_comprehensive_suite
    }
    
    if tier not in tier_map:
        raise ValueError(f"Unknown tier: {tier}. Available tiers: {list(tier_map.keys())}")
    
    return tier_map[tier]()