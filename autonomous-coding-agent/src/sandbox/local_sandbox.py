"""
Real local sandbox that actually clones and modifies repositories
"""

import asyncio
import logging
import uuid
import os
import shutil
import subprocess
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse
import tempfile

logger = logging.getLogger(__name__)


class LocalSandbox:
    """Real sandbox that actually clones and modifies repositories locally"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key
        self.active_sessions: Dict[str, Any] = {}
        self.base_temp_dir = tempfile.mkdtemp(prefix="autonomous_agent_")
        logger.info(f"Initialized LocalSandbox with temp dir: {self.base_temp_dir}")
    
    async def create_session(self) -> str:
        """Create a new sandbox session"""
        session_id = str(uuid.uuid4())
        session_dir = os.path.join(self.base_temp_dir, session_id)
        os.makedirs(session_dir, exist_ok=True)
        
        self.active_sessions[session_id] = {
            "working_dir": session_dir,
            "created_at": asyncio.get_event_loop().time(),
            "repo_dir": None
        }
        
        logger.info(f"Created real session: {session_id} at {session_dir}")
        return session_id
    
    async def clone_repository(self, session_id: str, repo_url: str) -> Dict[str, Any]:
        """Actually clone a real repository"""
        if session_id not in self.active_sessions:
            raise Exception(f"Session {session_id} not found")
        
        session = self.active_sessions[session_id]
        working_dir = session["working_dir"]
        
        # Parse repository URL to get repo name
        parsed_url = urlparse(repo_url)
        repo_name = parsed_url.path.rstrip('/').split('/')[-1]
        if repo_name.endswith('.git'):
            repo_name = repo_name[:-4]
        
        repo_dir = os.path.join(working_dir, repo_name)
        
        try:
            # Clone the repository
            clone_cmd = ["git", "clone", repo_url, repo_dir]
            result = subprocess.run(clone_cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                raise Exception(f"Failed to clone repository: {result.stderr}")
            
            # Count files in the repository
            files = []
            for root, dirs, filenames in os.walk(repo_dir):
                # Skip .git directory
                if '.git' in root:
                    continue
                for filename in filenames:
                    rel_path = os.path.relpath(os.path.join(root, filename), repo_dir)
                    files.append(rel_path)
            
            session["repo_dir"] = repo_dir
            session["repo_name"] = repo_name
            
            logger.info(f"Successfully cloned {repo_url} to {repo_dir}")
            logger.info(f"Found {len(files)} files in repository")
            
            return {
                "success": True,
                "repo_dir": repo_dir,
                "files": files,
                "file_count": len(files),
                "url": repo_url,
                "repo_name": repo_name
            }
            
        except subprocess.TimeoutExpired:
            raise Exception("Repository clone timed out")
        except Exception as e:
            logger.error(f"Failed to clone repository: {str(e)}")
            raise
    
    async def list_files(self, session_id: str, path: str = ".") -> List[str]:
        """List files in the repository"""
        if session_id not in self.active_sessions:
            raise Exception(f"Session {session_id} not found")
        
        session = self.active_sessions[session_id]
        repo_dir = session.get("repo_dir")
        
        if not repo_dir:
            raise Exception("No repository cloned in this session")
        
        target_path = os.path.join(repo_dir, path)
        if not os.path.exists(target_path):
            return []
        
        files = []
        if os.path.isdir(target_path):
            for item in os.listdir(target_path):
                item_path = os.path.join(target_path, item)
                if os.path.isfile(item_path):
                    files.append(item)
        
        return files
    
    async def read_file(self, session_id: str, file_path: str) -> Dict[str, Any]:
        """Read a file from the repository"""
        if session_id not in self.active_sessions:
            return {"success": False, "error": f"Session {session_id} not found"}
        
        session = self.active_sessions[session_id]
        repo_dir = session.get("repo_dir")
        
        if not repo_dir:
            return {"success": False, "error": "No repository cloned in this session"}
        
        full_path = os.path.join(repo_dir, file_path)
        
        if not os.path.exists(full_path):
            return {"success": False, "error": f"File {file_path} not found"}
        
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return {"success": True, "content": content}
        except Exception as e:
            return {"success": False, "error": f"Failed to read file {file_path}: {str(e)}"}
    
    async def write_file(self, session_id: str, file_path: str, content: str) -> Dict[str, Any]:
        """Write content to a file in the repository"""
        if session_id not in self.active_sessions:
            raise Exception(f"Session {session_id} not found")
        
        session = self.active_sessions[session_id]
        repo_dir = session.get("repo_dir")
        
        if not repo_dir:
            raise Exception("No repository cloned in this session")
        
        full_path = os.path.join(repo_dir, file_path)
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(full_path), exist_ok=True)
        
        try:
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            logger.info(f"Successfully wrote to file: {file_path}")
            return {"success": True, "file_path": file_path}
            
        except Exception as e:
            logger.error(f"Failed to write file {file_path}: {str(e)}")
            raise Exception(f"Failed to write file {file_path}: {str(e)}")
    
    async def execute_command(self, session_id: str, command: str, timeout: int = 30) -> Dict[str, Any]:
        """Execute a command in the repository directory"""
        if session_id not in self.active_sessions:
            raise Exception(f"Session {session_id} not found")
        
        session = self.active_sessions[session_id]
        repo_dir = session.get("repo_dir")
        
        if not repo_dir:
            raise Exception("No repository cloned in this session")
        
        # Ensure we're working in the repository directory
        if not os.path.exists(repo_dir):
            raise Exception(f"Repository directory not found: {repo_dir}")
        
        try:
            logger.debug(f"Executing command in {repo_dir}: {command}")
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=repo_dir
            )
            
            return {
                "success": result.returncode == 0,
                "exit_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "command": command
            }
            
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "exit_code": -1,
                "stdout": "",
                "stderr": f"Command timed out after {timeout} seconds",
                "command": command
            }
        except Exception as e:
            return {
                "success": False,
                "exit_code": -1,
                "stdout": "",
                "stderr": str(e),
                "command": command
            }
    
    async def setup_git_auth(self, session_id: str, github_token: str) -> Dict[str, Any]:
        """Setup git authentication for pushing"""
        if session_id not in self.active_sessions:
            raise Exception(f"Session {session_id} not found")
        
        session = self.active_sessions[session_id]
        repo_dir = session.get("repo_dir")
        
        if not repo_dir:
            raise Exception("No repository cloned in this session")
        
        try:
            # Configure git user
            await self.execute_command(session_id, 'git config user.name "Backspace Coding Agent"')
            await self.execute_command(session_id, 'git config user.email "<EMAIL>"')
            
            # Setup authentication by modifying the remote URL
            result = await self.execute_command(session_id, 'git remote get-url origin')
            if result["success"]:
                original_url = result["stdout"].strip()
                if original_url.startswith("https://github.com/"):
                    # Use proper token format for GitHub Personal Access Token
                    # Format: https://username:<EMAIL>/owner/repo.git
                    auth_url = original_url.replace("https://github.com/", f"https://oauth2:{github_token}@github.com/")
                    set_url_result = await self.execute_command(session_id, f'git remote set-url origin {auth_url}')
                    
                    if not set_url_result["success"]:
                        logger.error(f"Failed to set remote URL: {set_url_result['stderr']}")
                        return {"success": False, "error": f"Failed to set remote URL: {set_url_result['stderr']}"}
                    
                    # Verify the new URL was set
                    verify_result = await self.execute_command(session_id, 'git remote get-url origin')
                    if verify_result["success"]:
                        logger.info(f"Git remote URL updated successfully")
                    else:
                        logger.warning("Could not verify new remote URL")
                        
            return {"success": True}
            
        except Exception as e:
            logger.error(f"Error setting up git auth: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def push_branch(self, session_id: str, branch_name: str) -> Dict[str, Any]:
        """Push the branch to GitHub with better error handling"""
        
        # Check if we have commits on this branch that aren't on origin
        log_result = await self.execute_command(session_id, f'git log origin/{branch_name}..HEAD --oneline')
        
        # If the branch doesn't exist on origin yet, or we have new commits
        if not log_result["success"] or log_result["stdout"].strip():
            logger.info(f"Branch {branch_name} has commits to push")
        else:
            # Check if the branch exists locally but not remotely
            branch_check = await self.execute_command(session_id, f'git show-ref --verify --quiet refs/heads/{branch_name}')
            if not branch_check["success"]:
                return {"success": False, "error": f"Branch {branch_name} does not exist locally", "stderr": "Branch not found"}
        
        # Try to push the branch
        push_result = await self.execute_command(session_id, f'git push origin {branch_name}', timeout=60)
        
        if push_result["success"]:
            logger.info(f"Successfully pushed branch {branch_name}")
            return push_result
        else:
            # If push failed, try to set upstream and push
            logger.warning(f"Initial push failed, trying with upstream: {push_result['stderr']}")
            upstream_result = await self.execute_command(
                session_id, 
                f'git push --set-upstream origin {branch_name}', 
                timeout=60
            )
            
            if upstream_result["success"]:
                logger.info(f"Successfully pushed branch {branch_name} with upstream")
                return upstream_result
            else:
                logger.error(f"Push failed completely: {upstream_result['stderr']}")
                return upstream_result
    
    async def get_repo_info(self, session_id: str) -> Dict[str, Any]:
        """Get repository owner and name from git remote"""
        try:
            result = await self.execute_command(session_id, 'git remote get-url origin')
            if not result["success"]:
                return {"success": False, "error": "Could not get remote URL"}
            
            remote_url = result["stdout"].strip()
            
            # Parse GitHub URL to extract owner and repo name
            # Handle both HTTPS and SSH formats
            if "github.com" in remote_url:
                if remote_url.startswith("https://"):
                    # HTTPS format: https://github.com/owner/repo.git
                    # or https://oauth2:<EMAIL>/owner/repo.git
                    parts = remote_url.split("github.com/")[-1].split("/")
                    owner = parts[0]
                    repo_name = parts[1].replace(".git", "")
                elif remote_url.startswith("git@"):
                    # SSH format: **************:owner/repo.git
                    parts = remote_url.split(":")[-1].split("/")
                    owner = parts[0]
                    repo_name = parts[1].replace(".git", "")
                else:
                    return {"success": False, "error": f"Unsupported remote URL format: {remote_url}"}
                
                return {
                    "success": True,
                    "owner": owner,
                    "repo_name": repo_name,
                    "remote_url": remote_url
                }
            else:
                return {"success": False, "error": "Not a GitHub repository"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def create_pull_request(self, session_id: str, title: str, body: str, branch_name: str) -> Dict[str, Any]:
        """Create a pull request using GitHub API or return manual PR URL"""
        
        # Get repository info to construct URLs
        repo_info = await self.get_repo_info(session_id)
        if not repo_info.get("success"):
            return {"success": False, "error": "Could not get repository info"}
        
        owner = repo_info["owner"]
        repo_name = repo_info["repo_name"]
        
        # Try GitHub API first (more reliable than CLI)
        github_token = os.environ.get("GITHUB_TOKEN")
        if github_token:
            try:
                import requests
                
                # Create PR using GitHub API
                api_url = f"https://api.github.com/repos/{owner}/{repo_name}/pulls"
                headers = {
                    "Authorization": f"token {github_token}",
                    "Accept": "application/vnd.github.v3+json",
                    "User-Agent": "Backspace-Coding-Agent"
                }
                
                data = {
                    "title": title,
                    "body": body,
                    "head": branch_name,
                    "base": "main"
                }
                
                # Make API request
                response = requests.post(api_url, headers=headers, json=data, timeout=30)
                
                if response.status_code == 201:
                    pr_data = response.json()
                    pr_url = pr_data.get("html_url", "")
                    logger.info(f"Successfully created PR via GitHub API: {pr_url}")
                    return {
                        "success": True,
                        "pr_url": pr_url,
                        "stdout": pr_url,
                        "method": "github_api",
                        "pr_number": pr_data.get("number", 0)
                    }
                else:
                    logger.warning(f"GitHub API PR creation failed: {response.status_code} - {response.text}")
                    
            except Exception as e:
                logger.warning(f"GitHub API PR creation failed: {str(e)}")
        
        # Second fallback: try GitHub CLI if available
        gh_check = await self.execute_command(session_id, 'gh --version')
        
        if gh_check["success"]:
            # Set GitHub token for CLI
            if github_token:
                await self.execute_command(session_id, f'echo "{github_token}" | gh auth login --with-token')
            
            # GitHub CLI is available, try to create PR
            gh_result = await self.execute_command(
                session_id, 
                f'gh pr create --title "{title}" --body "{body}" --head {branch_name} --base main',
                timeout=30
            )
            
            if gh_result["success"]:
                # Extract PR URL from output
                pr_url = gh_result["stdout"].strip()
                logger.info(f"Successfully created PR via GitHub CLI: {pr_url}")
                return {
                    "success": True,
                    "pr_url": pr_url,
                    "stdout": pr_url,
                    "method": "github_cli"
                }
            else:
                # GitHub CLI failed, log the error
                logger.warning(f"GitHub CLI failed: {gh_result['stderr']}")
        
        # Final fallback: return manual PR creation URL
        manual_pr_url = f"https://github.com/{owner}/{repo_name}/compare/{branch_name}?expand=1"
        
        logger.info(f"Falling back to manual PR creation: {manual_pr_url}")
        return {
            "success": True,  # We consider this success since branch is pushed
            "pr_url": manual_pr_url,
            "stdout": manual_pr_url,
            "method": "manual",
            "message": "Branch pushed successfully. Please create PR manually using the URL above."
        }
    
    async def cleanup_session(self, session_id: str) -> None:
        """Clean up a session and its temporary files"""
        if session_id not in self.active_sessions:
            return
        
        session = self.active_sessions[session_id]
        working_dir = session["working_dir"]
        
        try:
            if os.path.exists(working_dir):
                shutil.rmtree(working_dir)
                logger.info(f"Cleaned up session {session_id}")
        except Exception as e:
            logger.error(f"Failed to cleanup session {session_id}: {str(e)}")
        
        del self.active_sessions[session_id]
    
    async def cleanup_all(self) -> None:
        """Clean up all sessions"""
        for session_id in list(self.active_sessions.keys()):
            await self.cleanup_session(session_id)
        
        # Clean up base temp directory
        try:
            if os.path.exists(self.base_temp_dir):
                shutil.rmtree(self.base_temp_dir)
                logger.info("Cleaned up all sandbox sessions")
        except Exception as e:
            logger.error(f"Failed to cleanup base temp dir: {str(e)}")
